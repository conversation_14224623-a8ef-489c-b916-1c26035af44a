﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!--<section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />-->
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
  </configSections>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <!--单点登录主站-->
    <add key="LoginServer" value="practice.nngeo.com" />
    <!--附件存储根目录-->
    <add key="RootPath" value="E:\Uploads" />
    <add key="PrivateKeyFile" value="C:\private.key" />
    <add key="LocalIdentity" value="nngeo.com" />
    <!--工程规划许可接口地址-->
    <add key="ServiceUrl" value="http://************:12666/TuWenControlle/SelectProjectInfo" />
    <add key="ServiceUrl1" value="http://jiansgc-beta.nnzrj.com/api/jsgcghxkz/noAuth/getTuWenData" />
    <!--市政工程规划许可接口地址-->
    <add key="SZServiceUrl" value="http://***********:12321/api/GH/GetSZGCGHXKZ" />
    <add key="xCloudApiUrl" value="http://guihhs-beta.nnzrj.com/api" />
    <!--测绘子系统-云平台接口地址-->
    <add key="CH_xCloudApiUrl" value="https://ch-beta.nnzrj.com/api" />
    <add key="EPSSDCServices" value="http://**********:8065/EPSSDCServices/EPSSDCServices.asmx" />
    <add key="EPSDownUrl" value="http://**********:8065/SDCDownloadFile/Download" />
    <!--EPS上传全面核实MDB检查接口-->
    <add key="EPSOverallUploadMDBUrl" value="http://172.16.3.184/SDCUploadService/api/Attachment/Upload" />
    <add key="CloudSmsServiceUrl" value="https://bdcsms.nnland.cn" />
    <add key="BDCSystemApiUrl" value="http://172.16.0.221:9000" />
    <add key="DataApiUrl" value="http://172.16.0.221:9900" />
    <add key="BDCWebServiceUrl" value="https://practice.nngeo.com" />
	<!--地理组征地拆迁MDB检查接口-->
	<add key="GeoScopeMDBUrl" value="http://172.16.48.133:5000/mdb" />
    <!--开放的业务流程-->
    <add key="EnableFlows" value="BaseSurveyDataDownloadFlow,BlueLineSurveyFlow,MarkPointSurveyFlow,MeasurePutLineFlow,RealEstatePreSurveyFlow,PlanCheckSurveyFlow,RealEstateActualSurveyFlow,RealEstatePreCheckSurveyFlow,RealEstatePreSurveyBuildingTableChangeFlow,RealEstatePreSurveyResultChangeFlow,RealEstateActualBuildingTableChangeFlow,RealEstateActualResultChangeFlow,MeasurePutLinePreCheckFlow,RealEstatePreCheckSurveyAutoFlow,CouncilPlanCheckFlow,CouncilMeasurePutLinePreCheckFlow,RealEstateOverallActualSurveyFlow,LandSurveyProjectFlow" />
    <add key="VisibleFlows" value="BaseSurveyDataDownloadFlow,MarkPointSurveyFlow,RealEstateActualSurveyFlow,RealEstatePreCheckSurveyFlow,RealEstatePreSurveyBuildingTableChangeFlow,RealEstatePreSurveyResultChangeFlow,RealEstateActualBuildingTableChangeFlow,RealEstateActualResultChangeFlow,MeasurePutLinePreCheckFlow,RealEstatePreCheckSurveyAutoFlow,CouncilPlanCheckFlow,CouncilMeasurePutLinePreCheckFlow,RealEstateOverallActualSurveyFlow,LandSurveyProjectFlow" />
    <!--云平台流程步骤显示文件-->
    <add key="XCloudStepFilePath" value=".Datas.XCloudSteps.json" />
  </appSettings>
  <system.web>
    <compilation debug="true" targetFramework="4.6.2" />
    <httpRuntime targetFramework="4.6.2" maxRequestLength="204800" executionTimeout="3600" requestValidationMode="2.0" enable="true" />
    <machineKey decryptionKey="B7EFF1C5839A624E3F97D0268917EDE82F408D2ECBFAC817" validation="SHA1" validationKey="C2B8DF31AB9624D69428066DFDA1A479542825F3B48865C4E47AF6A026F22D853DEC2B3248DF268599BF89EF78B9E86CA05AC73577E0D5A14C45E0267588850B" />
    <customErrors mode="Off"></customErrors>
  </system.web>
  <system.webServer>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
      <add name="DownloadAttachmentUrlHandler-Integrated-4.0" path="sdcapi/AttachmentManage/DownloadAttachmentWithKey/*" verb="GET" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="204800000" />
      </requestFiltering>
    </security>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <publisherPolicy apply="no" />
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.InteropServices.RuntimeInformation" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="v13.0" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
      <provider invariantName="Oracle.ManagedDataAccess.Client" type="Oracle.ManagedDataAccess.EntityFramework.EFOracleProviderServices, Oracle.ManagedDataAccess.EntityFramework, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </providers>
  </entityFramework>
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" />
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </DbProviderFactories>
  </system.data>
  <connectionStrings>
    <!--<add name="OracleDbContext" providerName="Oracle.ManagedDataAccess.Client" connectionString="User Id=oracle_user;Password=oracle_user_password;Data Source=oracle" />-->
    <add name="SDCWorkDB" providerName="Oracle.ManagedDataAccess.Client" connectionString="Data Source=************:11521/sdcdb;User Id=sdcwork;Password=*********" />
    <add name="HangfireMongoDB" connectionString="mongodb://localhost/SDCHangfireMongoDB_Practice" />
  </connectionStrings>
  <log4net>
    <!-- OFF, FATAL, ERROR, WARN, INFO, DEBUG, ALL -->
    <!-- Set root logger level to ERROR and its appenders -->
    <root>
      <level value="DEBUG" />
      <appender-ref ref="RollingFileTracer" />
    </root>
    <!-- Print only messages of level DEBUG or above in the packages -->
    <appender name="RollingFileTracer" type="log4net.Appender.RollingFileAppender,log4net">
      <param name="File" value="App_Data/Log/" />
      <param name="AppendToFile" value="true" />
      <param name="RollingStyle" value="Composite" />
      <param name="MaxSizeRollBackups" value="10" />
      <param name="MaximumFileSize" value="1MB" />
      <param name="DatePattern" value="&quot;Logs_&quot;yyyyMMdd&quot;.txt&quot;" />
      <param name="StaticLogFileName" value="false" />
      <layout type="log4net.Layout.PatternLayout,log4net">
        <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n" />
      </layout>
    </appender>
  </log4net>
</configuration>
