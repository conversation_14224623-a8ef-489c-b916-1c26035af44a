﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;

namespace SDCPCWeb.Models.BusinessContent.ZhengChai {
    public class LandSurvey_ContentInfoModel {
        /// <summary>
        /// 主键ID，与BusinessBaseInfo表的ID关联的外键
        /// </summary>
        public string ID { get; set; }
        
        /// <summary>
        /// 测绘成果汇总信息
        /// </summary>
        public string ProjectResultInfo { get; set; }

        /// <summary>
        /// 成果检查任务ID
        /// </summary>
        public string DataCheckID { get; set; }
        /// <summary>
        /// 成果检查状态 0 检查中  1 检查完成  2 入库成功 3 入库失败
        /// </summary>
        public int DataCheckState { get; set; }
        /// <summary>
        /// 注册测绘师姓名
        /// </summary>
        public string SurveyMasterName { get; set; }
        /// <summary>
        /// 注册测绘师身份证号
        /// </summary>
        public string SurveyMasterNo { get; set; }
        /// <summary>
        /// 注册测绘师确认时间
        /// </summary>
        public DateTime? SurveyMasterSureTime { get; set; }
        /// <summary>
        /// 校验入库失败的原因
        /// </summary>
        public string RejectMessage { get; set; }
    }
    public class LandSurvey_ContentInfo : LandSurvey_ContentInfoModel, IOracleDataTable {
        public LandSurvey_ContentInfoModel ToModel() {
            return JsonConvert.DeserializeObject<LandSurvey_ContentInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}